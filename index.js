import puppeteer from "puppeteer";

(async () => {
  const browser = await puppeteer.launch({
    headless: false, // must be false for screencast
    defaultViewport: null,
    args: ["--start-maximized"],
  });

  // Keep track of screencast sessions
  let activePage = null;
  let activeClient = null;

  // Helper to start screencast on a page
  async function startScreencast(page) {
    // Stop old screencast if any
    if (activeClient) {
      try {
        await activeClient.send("Page.stopScreencast");
      } catch (e) {}
    }

    // Attach CDP client
    const client = await page.target().createCDPSession();
    await client.send("Page.enable");

    // Bring page to front so it renders
    await client.send("Page.bringToFront");

    // Listen for frames
    client.on("Page.screencastFrame", async (frame) => {
      console.log("📸 Screencast frame received:", frame.metadata);

      // Normally you would forward `frame.data` (base64 JPEG) to your client
      // e.g. via WebSocket or WebRTC
      await client.send("Page.screencastFrameAck", {
        sessionId: frame.sessionId,
      });
    });

    // Start screencast
    await client.send("Page.startScreencast", {
      format: "jpeg",
      quality: 70,
      maxWidth: 1280,
      maxHeight: 720,
      everyNthFrame: 1,
    });

    activePage = page;
    activeClient = client;
    console.log("✅ Screencast started on:", page.url());
  }

  // Detect new popups from the current page
  function watchPage(page) {
    page.on("popup", async (popup) => {
      console.log("🆕 Popup detected:", popup.url());
      await startScreencast(popup);
      watchPage(popup);
    });

    page.on("framenavigated", (frame) => {
      if (frame === page.mainFrame()) {
        console.log("🔗 Navigated:", frame.url());
      }
    });
  }

  // Detect new targets (tabs/windows)
  browser.on("targetcreated", async (target) => {
    if (target.type() === "page") {
      const newPage = await target.page();
      console.log("🆕 New page/tab opened:", newPage.url());
      await startScreencast(newPage);
      watchPage(newPage);
    }
  });

  // Detect when tabs/windows are closed
  browser.on("targetdestroyed", async (target) => {
    console.log("❌ Target closed:", target.url());
    // Switch back to original if popup closes
    if (activePage && !activePage.isClosed()) {
      await startScreencast(activePage);
    }
  });

  // ---- Start flow ----
  const page = await browser.newPage();
  watchPage(page);

  await page.goto("https://uber.com", { waitUntil: "networkidle2" });
  await startScreencast(page);

  console.log("🌐 Opened Uber.com and streaming screencast...");
})();
