import puppeteer from "puppeteer";
import http from "http";
import { WebSocketServer } from "ws";

// Create HTTP server for the client
const server = http.createServer((req, res) => {
  if (req.url === "/") {
    res.writeHead(200, { "Content-Type": "text/html" });
    res.end(`
<!DOCTYPE html>
<html>
<head>
    <title>Screencast Viewer</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }
        #video {
            max-width: 100%;
            border: 2px solid #333;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Live Screencast</h1>
        <div id="status" class="status disconnected">Connecting...</div>
        <video id="video" autoplay muted></video>
    </div>

    <script>
        const video = document.getElementById('video');
        const status = document.getElementById('status');
        const ws = new WebSocket('ws://localhost:3001');

        ws.onopen = () => {
            status.textContent = '✅ Connected';
            status.className = 'status connected';
        };

        ws.onclose = () => {
            status.textContent = '❌ Disconnected';
            status.className = 'status disconnected';
        };

        ws.onmessage = (event) => {
            // Receive base64 JPEG frame and display it
            const frameData = event.data;
            video.src = 'data:image/jpeg;base64,' + frameData;
        };
    </script>
</body>
</html>
    `);
  } else {
    res.writeHead(404);
    res.end("Not found");
  }
});

// Create WebSocket server
const wss = new WebSocketServer({ port: 3001 });
let wsClients = new Set();

wss.on("connection", (ws) => {
  console.log("📱 Client connected");
  wsClients.add(ws);

  ws.on("close", () => {
    console.log("📱 Client disconnected");
    wsClients.delete(ws);
  });
});

// Function to broadcast frame to all connected clients
function broadcastFrame(frameData) {
  wsClients.forEach((ws) => {
    if (ws.readyState === ws.OPEN) {
      ws.send(frameData);
    }
  });
}

(async () => {
  const response = await fetch("http://localhost:9222/json/version");
  const wsEndpoint = await response.json();
  const browser = await puppeteer.connect({
    headless: false, // must be false for screencast
    defaultViewport: null,
    browserWSEndpoint: wsEndpoint.webSocketDebuggerUrl,
  });

  // Keep track of screencast sessions
  let activePage = null;
  let activeClient = null;

  // Helper to start screencast on a page
  async function startScreencast(page) {
    // Stop old screencast if any
    if (activeClient) {
      try {
        await activeClient.send("Page.stopScreencast");
      } catch (e) {}
    }

    // Attach CDP client
    const client = await page.target().createCDPSession();
    await client.send("Page.enable");

    // Bring page to front so it renders
    await client.send("Page.bringToFront");

    // Listen for frames
    client.on("Page.screencastFrame", async (frame) => {
      console.log("📸 Screencast frame received:", frame.metadata);

      // Broadcast frame data to all connected clients
      broadcastFrame(frame.data);

      await client.send("Page.screencastFrameAck", {
        sessionId: frame.sessionId,
      });
    });

    // Start screencast
    await client.send("Page.startScreencast", {
      format: "jpeg",
      quality: 70,
      maxWidth: 1280,
      maxHeight: 720,
      everyNthFrame: 1,
    });

    activePage = page;
    activeClient = client;
    console.log("✅ Screencast started on:", page.url());
  }

  // Detect new popups from the current page
  function watchPage(page) {
    page.on("popup", async (popup) => {
      console.log("🆕 Popup detected:", popup.url());
      await startScreencast(popup);
      watchPage(popup);
    });

    page.on("framenavigated", (frame) => {
      if (frame === page.mainFrame()) {
        console.log("🔗 Navigated:", frame.url());
      }
    });
  }

  // Detect new targets (tabs/windows)
  browser.on("targetcreated", async (target) => {
    if (target.type() === "page") {
      const newPage = await target.page();
      console.log("🆕 New page/tab opened:", newPage.url());
      await startScreencast(newPage);
      watchPage(newPage);
    }
  });

  // Detect when tabs/windows are closed
  browser.on("targetdestroyed", async (target) => {
    console.log("❌ Target closed:", target.url());
    // Switch back to original if popup closes
    if (activePage && !activePage.isClosed()) {
      await startScreencast(activePage);
    }
  });

  // ---- Start flow ----
  const page = await browser.newPage();
  watchPage(page);

  await page.goto("https://uber.com", { waitUntil: "networkidle2" });
  await startScreencast(page);

  console.log("🌐 Opened Uber.com and streaming screencast...");

  // Start HTTP server
  server.listen(3000, () => {
    console.log("🌐 Web client available at: http://localhost:3000");
    console.log("📡 WebSocket server running on port 3001");
  });
})();
