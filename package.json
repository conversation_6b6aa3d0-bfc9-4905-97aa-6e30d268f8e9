{"name": "streaming-poc", "version": "1.0.0", "description": "", "type": "module", "main": "index.js", "dependencies": {"agent-base": "^7.1.4", "ansi-regex": "^5.0.1", "ansi-styles": "^4.3.0", "argparse": "^2.0.1", "ast-types": "^0.13.4", "b4a": "^1.6.7", "bare-events": "^2.6.1", "bare-fs": "^4.2.1", "bare-os": "^3.6.2", "bare-path": "^3.0.0", "bare-stream": "^2.7.0", "basic-ftp": "^5.0.5", "buffer-crc32": "^0.2.13", "callsites": "^3.1.0", "chromium-bidi": "^8.0.0", "cliui": "^8.0.1", "color-convert": "^2.0.1", "color-name": "^1.1.4", "cosmiconfig": "^9.0.0", "data-uri-to-buffer": "^6.0.2", "debug": "^4.4.1", "degenerator": "^5.0.1", "devtools-protocol": "^0.0.1475386", "emoji-regex": "^8.0.0", "end-of-stream": "^1.4.5", "env-paths": "^2.2.1", "error-ex": "^1.3.2", "escalade": "^3.2.0", "escodegen": "^2.1.0", "esprima": "^4.0.1", "estraverse": "^5.3.0", "esutils": "^2.0.3", "extract-zip": "^2.0.1", "fast-fifo": "^1.3.2", "fd-slicer": "^1.1.0", "get-caller-file": "^2.0.5", "get-stream": "^5.2.0", "get-uri": "^6.0.5", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "import-fresh": "^3.3.1", "ip-address": "^10.0.1", "is-arrayish": "^0.2.1", "is-fullwidth-code-point": "^3.0.0", "js-tokens": "^4.0.0", "js-yaml": "^4.1.0", "json-parse-even-better-errors": "^2.3.1", "lines-and-columns": "^1.2.4", "lru-cache": "^7.18.3", "mitt": "^3.0.1", "ms": "^2.1.3", "netmask": "^2.0.2", "once": "^1.4.0", "pac-proxy-agent": "^7.2.0", "pac-resolver": "^7.0.1", "parent-module": "^1.0.1", "parse-json": "^5.2.0", "pend": "^1.2.0", "picocolors": "^1.1.1", "progress": "^2.0.3", "proxy-agent": "^6.5.0", "proxy-from-env": "^1.1.0", "pump": "^3.0.3", "puppeteer": "^24.17.0", "puppeteer-core": "^24.17.0", "require-directory": "^2.1.1", "resolve-from": "^4.0.0", "semver": "^7.7.2", "simple-cdp": "^1.8.6", "smart-buffer": "^4.2.0", "socks": "^2.8.7", "socks-proxy-agent": "^8.0.5", "source-map": "^0.6.1", "streamx": "^2.22.1", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "tar-fs": "^3.1.0", "tar-stream": "^3.1.7", "text-decoder": "^1.2.3", "tslib": "^2.8.1", "typed-query-selector": "^2.12.0", "undici-types": "^7.10.0", "wrap-ansi": "^7.0.0", "wrappy": "^1.0.2", "ws": "^8.18.3", "y18n": "^5.0.8", "yargs": "^17.7.2", "yargs-parser": "^21.1.1", "yauzl": "^2.10.0", "zod": "^3.25.76"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC"}